YM_AUDIO_MIXER_VERSION = 1.0
YM_AUDIO_MIXER_SITE = $(TOPDIR)/../audio_mixer_rk3576
YM_AUDIO_MIXER_SITE_METHOD = local
YM_AUDIO_MIXER_DEPENDENCIES = openssl

define YM_AUDIO_MIXER_BUILD_CMDS
    cd $(YM_AUDIO_MIXER_SITE); ./build.sh all; cd -
endef

define YM_AUDIO_MIXER_CLEAN_CMDS
    cd $(YM_AUDIO_MIXER_SITE); ./build.sh clean; cd -
endef

define YM_AUDIO_MIXER_INSTALL_TARGET_CMDS
    cd $(YM_AUDIO_MIXER_SITE); ./build.sh install; cd -
endef

define YM_AUDIO_MIXER_POST_BUILD_HOOK
    rm -f $(@D)/.stamp_*
endef
YM_AUDIO_MIXER_POST_BUILD_HOOKS += YM_AUDIO_MIXER_POST_BUILD_HOOK

$(eval $(generic-package))
